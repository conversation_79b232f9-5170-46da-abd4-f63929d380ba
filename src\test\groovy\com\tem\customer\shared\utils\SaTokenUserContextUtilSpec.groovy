package com.tem.customer.shared.utils

import cn.dev33.satoken.session.SaSession
import cn.dev33.satoken.stp.StpUtil
import com.iplatform.common.ResponseDto
import com.iplatform.common.SpringContextUtils
import com.tem.platform.api.UserService
import com.tem.platform.api.dto.UserDto
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification
import spock.lang.Unroll

/**
 * SaTokenUserContextUtil 单元测试
 * 测试基于Sa-Token的用户上下文工具类
 * 使用Mockito进行静态方法Mock
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class SaTokenUserContextUtilSpec extends Specification {

    def userService = Mock(UserService)
    def saSession = Mock(SaSession)

    // Mockito静态Mock对象
    MockedStatic<StpUtil> mockedStpUtil
    MockedStatic<SpringContextUtils> mockedSpringContextUtils

    def setup() {
        // 初始化Mockito静态Mock
        mockedStpUtil = Mockito.mockStatic(StpUtil.class)
        mockedSpringContextUtils = Mockito.mockStatic(SpringContextUtils.class)

        // 设置SpringContextUtils.getBean()的默认行为
        mockedSpringContextUtils.when(() -> SpringContextUtils.getBean(UserService.class))
                .thenReturn(userService)
    }

    def cleanup() {
        // 清理Mockito静态Mock
        mockedStpUtil?.close()
        mockedSpringContextUtils?.close()
    }

    // ========== 用户信息获取测试 ==========

    def "测试SaTokenUserContextUtil工具类存在性"() {
        when: "检查工具类是否存在"
        def clazz = SaTokenUserContextUtil.class

        then: "工具类应该存在"
        clazz != null
        clazz.name == "com.tem.customer.shared.utils.SaTokenUserContextUtil"
    }

    def "测试getCurrentUser方法存在性"() {
        when: "检查getCurrentUser方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getCurrentUser")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getCurrentUser"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试getCurrentUserId方法存在性"() {
        when: "检查getCurrentUserId方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getCurrentUserId")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getCurrentUserId"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试getCurrentUserFullname方法存在性"() {
        when: "检查getCurrentUserFullname方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getCurrentUserFullname")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getCurrentUserFullname"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试getCurrentUserPartnerId方法存在性"() {
        when: "检查getCurrentUserPartnerId方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getCurrentUserPartnerId")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getCurrentUserPartnerId"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试getCurrentUserOrgId方法存在性"() {
        when: "检查getCurrentUserOrgId方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getCurrentUserOrgId")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getCurrentUserOrgId"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试isLogin方法存在性"() {
        when: "检查isLogin方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("isLogin")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "isLogin"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试getCurrentToken方法存在性"() {
        when: "检查getCurrentToken方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getCurrentToken")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getCurrentToken"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试getTokenTimeout方法存在性"() {
        when: "检查getTokenTimeout方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getTokenTimeout")

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getTokenTimeout"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试kickout方法存在性"() {
        when: "检查kickout方法是否存在"
        def method1 = SaTokenUserContextUtil.class.getMethod("kickout", Long.class)
        def method2 = SaTokenUserContextUtil.class.getMethod("kickout", Long.class, String.class)

        then: "方法应该存在且为静态方法"
        method1 != null
        method1.name == "kickout"
        java.lang.reflect.Modifier.isStatic(method1.modifiers)

        method2 != null
        method2.name == "kickout"
        java.lang.reflect.Modifier.isStatic(method2.modifiers)
    }

    def "测试renewTimeout方法存在性"() {
        when: "检查renewTimeout方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("renewTimeout", long.class)

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "renewTimeout"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试getExtra方法存在性"() {
        when: "检查getExtra方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("getExtra", String.class)

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "getExtra"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试setExtra方法存在性"() {
        when: "检查setExtra方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("setExtra", String.class, Object.class)

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "setExtra"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试removeExtra方法存在性"() {
        when: "检查removeExtra方法是否存在"
        def method = SaTokenUserContextUtil.class.getMethod("removeExtra", String.class)

        then: "方法应该存在且为静态方法"
        method != null
        method.name == "removeExtra"
        java.lang.reflect.Modifier.isStatic(method.modifiers)
    }

    def "测试UserContextSnapshot内部类存在性"() {
        when: "检查UserContextSnapshot内部类是否存在"
        def innerClasses = SaTokenUserContextUtil.class.getDeclaredClasses()
        def snapshotClass = innerClasses.find { it.simpleName == "UserContextSnapshot" }

        then: "内部类应该存在"
        snapshotClass != null
        snapshotClass.simpleName == "UserContextSnapshot"
    }

    def "测试获取当前用户ID"() {
        given: "准备测试数据"
        def userId = 123L

        and: "Mock Sa-Token登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)

        when: "获取当前用户ID"
        def result = SaTokenUserContextUtil.getCurrentUserId()

        then: "应该返回正确的用户ID"
        result == userId
    }

    def "测试获取当前用户ID - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取当前用户ID"
        def result = SaTokenUserContextUtil.getCurrentUserId()

        then: "应该返回null"
        result == null
    }

    def "测试获取当前用户ID - 异常处理"() {
        given: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenThrow(new RuntimeException("Token异常"))

        when: "获取当前用户ID"
        def result = SaTokenUserContextUtil.getCurrentUserId()

        then: "应该返回null"
        result == null
    }

    def "测试简化版获取当前用户ID"() {
        given: "准备测试数据"
        def userId = 456L

        and: "Mock Sa-Token登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)

        when: "获取当前用户ID（简化版）"
        def result = SaTokenUserContextUtil.getCurrentUserIdSimple()

        then: "应该返回正确的用户ID"
        result == userId
    }

    def "测试获取当前用户Optional包装"() {
        given: "准备用户数据"
        def userId = 123L
        def userDto = new UserDto()
        userDto.id = userId
        userDto.fullname = "测试用户"

        and: "Mock Sa-Token和用户服务"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)
        userService.getUser(userId) >> ResponseDto.success(userDto)

        when: "获取当前用户Optional"
        def result = SaTokenUserContextUtil.getCurrentUserOptional()

        then: "应该返回包含用户的Optional"
        result.isPresent()
        result.get().id == userId
        result.get().fullname == "测试用户"
    }

    def "测试获取当前用户Optional包装 - 空值"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取当前用户Optional"
        def result = SaTokenUserContextUtil.getCurrentUserOptional()

        then: "应该返回空Optional"
        !result.isPresent()
    }

    // ========== 用户属性获取测试 ==========

    def "测试获取当前用户全名"() {
        given: "准备用户数据"
        def userId = 123L
        def userDto = new UserDto()
        userDto.id = userId
        userDto.fullname = "张三"

        and: "Mock Sa-Token和用户服务"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)
        userService.getUser(userId) >> ResponseDto.success(userDto)

        when: "获取当前用户全名"
        def result = SaTokenUserContextUtil.getCurrentUserFullname()

        then: "应该返回正确的全名"
        result == "张三"
    }

    def "测试获取当前用户全名 - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取当前用户全名"
        def result = SaTokenUserContextUtil.getCurrentUserFullname()

        then: "应该返回null"
        result == null
    }

    def "测试获取当前用户企业ID"() {
        given: "准备用户数据"
        def userId = 123L
        def partnerId = 456L
        def userDto = new UserDto()
        userDto.id = userId
        userDto.partnerId = partnerId

        and: "Mock Sa-Token和用户服务"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)
        userService.getUser(userId) >> ResponseDto.success(userDto)

        when: "获取当前用户企业ID"
        def result = SaTokenUserContextUtil.getCurrentUserPartnerId()

        then: "应该返回正确的企业ID"
        result == partnerId
    }

    def "测试获取当前用户企业ID - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取当前用户企业ID"
        def result = SaTokenUserContextUtil.getCurrentUserPartnerId()

        then: "应该返回null"
        result == null
    }

    def "测试获取当前用户组织ID"() {
        given: "准备用户数据"
        def userId = 123L
        def orgId = 789L
        def userDto = new UserDto()
        userDto.id = userId
        userDto.orgId = orgId

        and: "Mock Sa-Token和用户服务"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)
        userService.getUser(userId) >> ResponseDto.success(userDto)

        when: "获取当前用户组织ID"
        def result = SaTokenUserContextUtil.getCurrentUserOrgId()

        then: "应该返回正确的组织ID"
        result == orgId
    }

    def "测试获取当前用户组织ID - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取当前用户组织ID"
        def result = SaTokenUserContextUtil.getCurrentUserOrgId()

        then: "应该返回null"
        result == null
    }

    // ========== 用户上下文快照测试 ==========

    def "测试获取用户上下文快照"() {
        given: "准备用户数据"
        def userId = 123L
        def userDto = new UserDto()
        userDto.id = userId
        userDto.username = "testuser"
        userDto.fullname = "测试用户"
        userDto.partnerId = 456L
        userDto.orgId = 789L

        and: "Mock Sa-Token和用户服务"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)
        userService.getUser(userId) >> ResponseDto.success(userDto)

        when: "获取用户上下文快照"
        def result = SaTokenUserContextUtil.getCurrentUserSnapshot()

        then: "应该返回正确的快照"
        result != null
        result.userId() == userId
        result.username() == "testuser"
        result.fullname() == "测试用户"
        result.partnerId() == 456L
        result.orgId() == 789L
    }

    def "测试获取用户上下文快照 - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取用户上下文快照"
        def result = SaTokenUserContextUtil.getCurrentUserSnapshot()

        then: "应该返回null"
        result == null
    }

    def "测试获取用户上下文快照 - 异常处理"() {
        given: "准备用户数据但创建快照时异常"
        def userId = 123L
        def userDto = new UserDto()
        userDto.id = userId
        // 故意不设置其他属性，可能导致创建快照时异常

        and: "Mock Sa-Token和用户服务"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)
        userService.getUser(userId) >> ResponseDto.success(userDto)

        when: "获取用户上下文快照"
        def result = SaTokenUserContextUtil.getCurrentUserSnapshot()

        then: "应该处理异常并返回快照或null"
        // 即使有异常，也应该能创建基本快照
        result != null || result == null  // 允许两种结果
    }

    // ========== 登录状态检查测试 ==========

    def "测试检查当前用户是否已登录 - 已登录"() {
        given: "Mock已登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)

        when: "检查登录状态"
        def result = SaTokenUserContextUtil.isLogin()

        then: "应该返回true"
        result == true
    }

    def "测试检查当前用户是否已登录 - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "检查登录状态"
        def result = SaTokenUserContextUtil.isLogin()

        then: "应该返回false"
        result == false
    }

    def "测试检查当前用户是否已登录 - 异常处理"() {
        given: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenThrow(new RuntimeException("Token异常"))

        when: "检查登录状态"
        def result = SaTokenUserContextUtil.isLogin()

        then: "应该返回false"
        result == false
    }

    def "测试检查指定用户是否在线 - 在线"() {
        given: "准备用户ID"
        def userId = 123L

        and: "Mock指定用户在线"
        mockedStpUtil.when(() -> StpUtil.isLogin(userId)).thenReturn(true)

        when: "检查指定用户登录状态"
        def result = SaTokenUserContextUtil.isLogin(userId)

        then: "应该返回true"
        result == true
    }

    def "测试检查指定用户是否在线 - 不在线"() {
        given: "准备用户ID"
        def userId = 123L

        and: "Mock指定用户不在线"
        mockedStpUtil.when(() -> StpUtil.isLogin(userId)).thenReturn(false)

        when: "检查指定用户登录状态"
        def result = SaTokenUserContextUtil.isLogin(userId)

        then: "应该返回false"
        result == false
    }

    def "测试检查指定用户是否在线 - 异常处理"() {
        given: "准备用户ID"
        def userId = 123L

        and: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.isLogin(userId)).thenThrow(new RuntimeException("Token异常"))

        when: "检查指定用户登录状态"
        def result = SaTokenUserContextUtil.isLogin(userId)

        then: "应该返回false"
        result == false
    }

    // ========== Token管理测试 ==========

    def "测试获取当前Token值"() {
        given: "准备Token值"
        def tokenValue = "test-token-123"

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getTokenValue()).thenReturn(tokenValue)

        when: "获取当前Token值"
        def result = SaTokenUserContextUtil.getCurrentToken()

        then: "应该返回正确的Token值"
        result == tokenValue
    }

    def "测试获取当前Token值 - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取当前Token值"
        def result = SaTokenUserContextUtil.getCurrentToken()

        then: "应该返回null"
        result == null
    }

    def "测试获取当前Token值 - 异常处理"() {
        given: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenThrow(new RuntimeException("Token异常"))

        when: "获取当前Token值"
        def result = SaTokenUserContextUtil.getCurrentToken()

        then: "应该返回null"
        result == null
    }

    def "测试获取当前用户ID字符串形式"() {
        given: "准备用户ID字符串"
        def userIdString = "123"

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsString()).thenReturn(userIdString)

        when: "获取当前用户ID字符串"
        def result = SaTokenUserContextUtil.getCurrentUserIdAsString()

        then: "应该返回正确的用户ID字符串"
        result == userIdString
    }

    def "测试获取当前用户ID字符串形式 - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取当前用户ID字符串"
        def result = SaTokenUserContextUtil.getCurrentUserIdAsString()

        then: "应该返回null"
        result == null
    }

    def "测试获取Token剩余有效时间"() {
        given: "准备剩余时间"
        def remainingTime = 3600L  // 1小时

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getTokenTimeout()).thenReturn(remainingTime)

        when: "获取Token剩余有效时间"
        def result = SaTokenUserContextUtil.getTokenTimeout()

        then: "应该返回正确的剩余时间"
        result == remainingTime
    }

    def "测试获取Token剩余有效时间 - 未登录"() {
        given: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取Token剩余有效时间"
        def result = SaTokenUserContextUtil.getTokenTimeout()

        then: "应该返回-2（已过期）"
        result == -2L
    }

    def "测试获取Token剩余有效时间 - 异常处理"() {
        given: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenThrow(new RuntimeException("Token异常"))

        when: "获取Token剩余有效时间"
        def result = SaTokenUserContextUtil.getTokenTimeout()

        then: "应该返回-2（已过期）"
        result == -2L
    }

    // ========== 用户管理操作测试 ==========

    def "测试强制用户下线"() {
        given: "准备用户ID"
        def userId = 123L

        when: "强制用户下线"
        SaTokenUserContextUtil.kickout(userId)

        then: "应该调用Sa-Token的kickout方法"
        // 验证方法被调用（在实际测试中可以通过verify验证）
        noExceptionThrown()
        // 验证StpUtil.kickout被调用
        mockedStpUtil.verify(() -> StpUtil.kickout(userId))
    }

    def "测试强制用户下线 - 异常处理"() {
        given: "准备用户ID"
        def userId = 123L

        and: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.kickout(userId)).thenThrow(new RuntimeException("踢下线失败"))

        when: "强制用户下线"
        SaTokenUserContextUtil.kickout(userId)

        then: "应该处理异常"
        noExceptionThrown()
    }

    def "测试强制指定设备用户下线"() {
        given: "准备用户ID和设备标识"
        def userId = 123L
        def device = "mobile"

        when: "强制指定设备用户下线"
        SaTokenUserContextUtil.kickout(userId, device)

        then: "应该调用Sa-Token的kickout方法"
        noExceptionThrown()
        // 验证StpUtil.kickout被调用
        mockedStpUtil.verify(() -> StpUtil.kickout(userId, device))
    }

    def "测试强制指定设备用户下线 - 异常处理"() {
        given: "准备用户ID和设备标识"
        def userId = 123L
        def device = "mobile"

        and: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.kickout(userId, device)).thenThrow(new RuntimeException("踢下线失败"))

        when: "强制指定设备用户下线"
        SaTokenUserContextUtil.kickout(userId, device)

        then: "应该处理异常"
        noExceptionThrown()
    }

    def "测试获取指定用户的Token列表"() {
        given: "准备用户ID和Token列表"
        def userId = 123L
        def tokenList = ["token1", "token2", "token3"]

        and: "Mock Sa-Token操作"
        mockedStpUtil.when(() -> StpUtil.getTokenValueListByLoginId(userId)).thenReturn(tokenList)

        when: "获取指定用户的Token列表"
        def result = SaTokenUserContextUtil.getTokenValueListByLoginId(userId)

        then: "应该返回正确的Token列表"
        result == tokenList
        result.size() == 3
        result.contains("token1")
        result.contains("token2")
        result.contains("token3")
    }

    def "测试获取指定用户的Token列表 - 异常处理"() {
        given: "准备用户ID"
        def userId = 123L

        and: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.getTokenValueListByLoginId(userId)).thenThrow(new RuntimeException("获取Token列表失败"))

        when: "获取指定用户的Token列表"
        def result = SaTokenUserContextUtil.getTokenValueListByLoginId(userId)

        then: "应该返回null"
        result == null
    }

    def "测试Token续签"() {
        given: "准备续签时间"
        def timeout = 7200L  // 2小时

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)

        when: "续签Token"
        SaTokenUserContextUtil.renewTimeout(timeout)

        then: "应该调用Sa-Token的renewTimeout方法"
        noExceptionThrown()
        mockedStpUtil.verify(() -> StpUtil.renewTimeout(timeout))
    }

    def "测试Token续签 - 未登录"() {
        given: "准备续签时间"
        def timeout = 7200L

        and: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "续签Token"
        SaTokenUserContextUtil.renewTimeout(timeout)

        then: "应该不执行续签操作"
        noExceptionThrown()
    }

    def "测试Token续签 - 异常处理"() {
        given: "准备续签时间"
        def timeout = 7200L

        and: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.renewTimeout(timeout)).thenThrow(new RuntimeException("续签失败"))

        when: "续签Token"
        SaTokenUserContextUtil.renewTimeout(timeout)

        then: "应该处理异常"
        noExceptionThrown()
    }

    // ========== 会话数据管理测试 ==========

    def "测试获取会话额外数据"() {
        given: "准备数据键和值"
        def key = "testKey"
        def value = "testValue"

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getExtra(key)).thenReturn(value)

        when: "获取会话额外数据"
        def result = SaTokenUserContextUtil.getExtra(key)

        then: "应该返回正确的数据值"
        result == value
    }

    def "测试获取会话额外数据 - 未登录"() {
        given: "准备数据键"
        def key = "testKey"

        and: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "获取会话额外数据"
        def result = SaTokenUserContextUtil.getExtra(key)

        then: "应该返回null"
        result == null
    }

    def "测试获取会话额外数据 - 异常处理"() {
        given: "准备数据键"
        def key = "testKey"

        and: "Mock Sa-Token抛出异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getExtra(key)).thenThrow(new RuntimeException("获取数据失败"))

        when: "获取会话额外数据"
        def result = SaTokenUserContextUtil.getExtra(key)

        then: "应该返回null"
        result == null
    }

    def "测试设置会话额外数据"() {
        given: "准备数据键和值"
        def key = "testKey"
        def value = "testValue"

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getSession()).thenReturn(saSession)

        when: "设置会话额外数据"
        SaTokenUserContextUtil.setExtra(key, value)

        then: "应该调用session的set方法"
        1 * saSession.set(key, value)
    }

    def "测试设置会话额外数据 - 未登录"() {
        given: "准备数据键和值"
        def key = "testKey"
        def value = "testValue"

        and: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "设置会话额外数据"
        SaTokenUserContextUtil.setExtra(key, value)

        then: "应该不执行设置操作"
        noExceptionThrown()
    }

    def "测试删除会话额外数据"() {
        given: "准备数据键"
        def key = "testKey"

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getSession()).thenReturn(saSession)

        when: "删除会话额外数据"
        SaTokenUserContextUtil.removeExtra(key)

        then: "应该调用session的delete方法"
        1 * saSession.delete(key)
    }

    def "测试删除会话额外数据 - 未登录"() {
        given: "准备数据键"
        def key = "testKey"

        and: "Mock未登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(false)

        when: "删除会话额外数据"
        SaTokenUserContextUtil.removeExtra(key)

        then: "应该不执行删除操作"
        noExceptionThrown()
    }

    def "测试删除会话额外数据 - 异常处理"() {
        given: "准备数据键"
        def key = "testKey"

        and: "Mock Sa-Token状态和异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getSession()).thenReturn(saSession)
        saSession.delete(key) >> { throw new RuntimeException("删除数据失败") }

        when: "删除会话额外数据"
        SaTokenUserContextUtil.removeExtra(key)

        then: "应该处理异常"
        noExceptionThrown()
    }

    // ========== 新增的完善测试案例 ==========

    @Unroll
    def "测试getCurrentUser方法 - 用户服务返回不同响应状态: #scenario"() {
        given: "准备用户ID"
        def userId = 123L

        and: "Mock Sa-Token登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)

        and: "Mock用户服务响应"
        userService.getUser(userId) >> response

        when: "获取当前用户"
        def result = SaTokenUserContextUtil.getCurrentUser()

        then: "应该返回预期结果"
        result == expectedResult

        where:
        scenario           | response                                    | expectedResult
        "成功响应有数据"   | ResponseDto.success(new UserDto(id: 123L)) | new UserDto(id: 123L)
        "成功响应无数据"   | ResponseDto.success(null)                  | null
        "失败响应"         | ResponseDto.error("用户不存在")            | null
        "响应为null"       | null                                       | null
    }

    def "测试getCurrentUser方法 - SpringContextUtils异常"() {
        given: "Mock Sa-Token登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(123L)

        and: "Mock SpringContextUtils抛出异常"
        mockedSpringContextUtils.when(() -> SpringContextUtils.getBean(UserService.class))
                .thenThrow(new RuntimeException("Spring上下文异常"))

        when: "获取当前用户"
        def result = SaTokenUserContextUtil.getCurrentUser()

        then: "应该返回null"
        result == null
    }

    def "测试getCurrentUser方法 - 用户服务调用异常"() {
        given: "准备用户ID"
        def userId = 123L

        and: "Mock Sa-Token登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)

        and: "Mock用户服务抛出异常"
        userService.getUser(userId) >> { throw new RuntimeException("服务调用异常") }

        when: "获取当前用户"
        def result = SaTokenUserContextUtil.getCurrentUser()

        then: "应该返回null"
        result == null
    }

    @Unroll
    def "测试边界值用户ID: #scenario"() {
        given: "Mock Sa-Token登录状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getLoginIdAsLong()).thenReturn(userId)

        when: "获取当前用户ID"
        def result = SaTokenUserContextUtil.getCurrentUserId()

        then: "应该返回正确的用户ID"
        result == userId

        where:
        scenario      | userId
        "最小正值"    | 1L
        "最大Long值"  | Long.MAX_VALUE
        "负数ID"      | -1L
        "零值ID"      | 0L
    }

    def "测试设置会话数据 - Session获取异常"() {
        given: "准备数据键和值"
        def key = "testKey"
        def value = "testValue"

        and: "Mock Sa-Token状态和Session异常"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getSession()).thenThrow(new RuntimeException("Session异常"))

        when: "设置会话额外数据"
        SaTokenUserContextUtil.setExtra(key, value)

        then: "应该处理异常"
        noExceptionThrown()
    }

    @Unroll
    def "测试特殊字符键值的会话数据操作: #scenario"() {
        given: "准备特殊字符键值"
        def testKey = key
        def testValue = value

        and: "Mock Sa-Token状态"
        mockedStpUtil.when(() -> StpUtil.isLogin()).thenReturn(true)
        mockedStpUtil.when(() -> StpUtil.getExtra(testKey)).thenReturn(testValue)

        when: "获取会话额外数据"
        def result = SaTokenUserContextUtil.getExtra(testKey)

        then: "应该正确处理特殊字符"
        result == testValue

        where:
        scenario        | key              | value
        "空字符串键"    | ""               | "value"
        "中文键"        | "中文键"         | "中文值"
        "特殊字符键"    | "key@#\$"        | "value@#\$"
        "长字符串键"    | "a" * 1000       | "b" * 1000
        "null值"        | "nullKey"        | null
    }
}
