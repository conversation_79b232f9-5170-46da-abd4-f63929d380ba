package com.tem.customer.shared.utils

import org.slf4j.MDC
import spock.lang.Specification
import spock.lang.Timeout

import java.util.concurrent.*
/**
 * ThreadPoolUtil 单元测试
 * 测试线程池工具类的任务提交、TraceId传递、状态监控等功能
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
class ThreadPoolUtilSpec extends Specification {

    private ExecutorService mockExecutorService
    private ThreadPoolUtil threadPoolUtil
    private Future mockFuture

    def setup() {
        // 创建Mock对象
        mockExecutorService = Mock(ExecutorService)
        mockFuture = Mock(Future)

        // 创建ThreadPoolUtil实例并初始化
        threadPoolUtil = new ThreadPoolUtil(mockExecutorService)
        threadPoolUtil.init()
    }

    def cleanup() {
        // 清理MDC
        MDC.clear()
    }

    def "测试获取线程池执行器"() {
        when: "获取线程池执行器"
        def executor = ThreadPoolUtil.getExecutor()

        then: "应该返回非空的执行器"
        executor != null
    }

    def "测试提交Runnable任务"() {
        given: "准备一个简单的Runnable任务"
        def task = new Runnable() {
            @Override
            void run() {
                // Mock任务
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)

        then: "应该调用ExecutorService的submit方法"
        1 * mockExecutorService.submit(_ as Runnable) >> mockFuture
        future == mockFuture
    }

    def "测试提交Callable任务"() {
        given: "准备一个Callable任务"
        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                return "任务执行成功"
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)

        then: "应该调用ExecutorService的submit方法"
        1 * mockExecutorService.submit(_ as Callable) >> mockFuture
        future == mockFuture
    }

    @Timeout(10)
    def "测试TraceId传递 - Runnable任务"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def originalTraceId = "test-trace-id-123"
        MDC.put("traceId", originalTraceId)

        def capturedTraceId = null
        def latch = new CountDownLatch(1)

        def task = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "异步任务中应该能获取到TraceId"
        capturedTraceId == originalTraceId

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    @Timeout(10)
    def "测试TraceId传递 - Callable任务"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def originalTraceId = "test-trace-id-456"
        MDC.put("traceId", originalTraceId)

        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                return MDC.get("traceId")
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)
        def capturedTraceId = future.get(5, TimeUnit.SECONDS)

        then: "异步任务中应该能获取到TraceId"
        capturedTraceId == originalTraceId

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    def "测试无TraceId时的任务执行"() {
        given: "清除当前线程的TraceId"
        MDC.clear()

        def task = new Runnable() {
            @Override
            void run() {
                // Mock任务
            }
        }

        when: "提交任务"
        def future = ThreadPoolUtil.submit(task)

        then: "任务应该正常执行"
        1 * mockExecutorService.submit(_ as Runnable) >> mockFuture
        future == mockFuture
    }

    def "测试获取线程池状态 - Mock ExecutorService"() {
        when: "获取线程池状态"
        def status = ThreadPoolUtil.getThreadPoolStatus()

        then: "Mock ExecutorService应该返回null"
        status == null
    }

    def "测试获取线程池状态 - 真实ThreadPoolExecutor"() {
        given: "使用真实的ThreadPoolExecutor"
        def realExecutor = new ThreadPoolExecutor(
            2, 4, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(10)
        )
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        when: "获取线程池状态"
        def status = ThreadPoolUtil.getThreadPoolStatus()

        then: "应该返回有效的状态信息"
        status != null
        status.poolName == "DEFAULT"
        status.corePoolSize == 2
        status.maximumPoolSize == 4
        status.activeCount >= 0
        status.poolSize >= 0
        status.queueSize >= 0
        status.completedTaskCount >= 0
        status.taskCount >= 0
        status.isShutdown == false
        status.isTerminated == false

        cleanup:
        realExecutor.shutdown()
    }

    def "测试并发任务执行"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(3)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def taskCount = 10
        def latch = new CountDownLatch(taskCount)
        def results = Collections.synchronizedList(new ArrayList<Integer>())

        def tasks = (1..taskCount).collect { i ->
            new Runnable() {
                @Override
                void run() {
                    results.add(i)
                    latch.countDown()
                }
            }
        }

        when: "提交所有任务"
        tasks.each { task ->
            ThreadPoolUtil.submit(task)
        }
        latch.await(10, TimeUnit.SECONDS)

        then: "所有任务都应该被执行"
        results.size() == taskCount
        results.containsAll(1..taskCount)

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    def "测试任务异常处理"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                throw new RuntimeException("任务执行异常")
            }
        }

        when: "提交任务并获取结果"
        def future = ThreadPoolUtil.submit(task)
        future.get(5, TimeUnit.SECONDS)

        then: "应该抛出异常"
        def ex = thrown(Exception)
        ex.cause.message == "任务执行异常"

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    @Timeout(10)
    def "测试MDC上下文完整传递"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        MDC.put("traceId", "trace-123")
        MDC.put("userId", "user-456")
        MDC.put("requestId", "req-789")

        def capturedMDC = [:]
        def latch = new CountDownLatch(1)

        def task = new Runnable() {
            @Override
            void run() {
                capturedMDC.putAll(MDC.getCopyOfContextMap() ?: [:])
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "所有MDC属性都应该被传递"
        capturedMDC["traceId"] == "trace-123"
        capturedMDC["userId"] == "user-456"
        capturedMDC["requestId"] == "req-789"

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    def "测试线程池状态监控"() {
        given: "使用真实的ThreadPoolExecutor进行集成测试"
        def realExecutor = new ThreadPoolExecutor(
            2, 4, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(10)
        )
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def taskCount = 5
        def latch = new CountDownLatch(taskCount)

        (1..taskCount).each { i ->
            ThreadPoolUtil.submit(new Runnable() {
                @Override
                void run() {
                    Thread.sleep(100) // 短暂延迟
                    latch.countDown()
                }
            })
        }

        when: "获取线程池状态"
        def status = ThreadPoolUtil.getThreadPoolStatus()
        latch.await(10, TimeUnit.SECONDS)
        def finalStatus = ThreadPoolUtil.getThreadPoolStatus()

        then: "状态信息应该反映线程池的变化"
        status != null
        finalStatus != null
        finalStatus.completedTaskCount >= status.completedTaskCount

        cleanup:
        realExecutor.shutdown()
    }

    def "测试自定义线程工厂"() {
        given: "使用真实的线程池和自定义线程工厂进行集成测试"
        def threadFactory = new ThreadPoolUtil.BenefitsThreadFactory("customer-admin-web")
        def realExecutor = new ThreadPoolExecutor(
            1, 1, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(10),
            threadFactory
        )
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def threadName = null
        def latch = new CountDownLatch(1)

        def task = new Runnable() {
            @Override
            void run() {
                threadName = Thread.currentThread().getName()
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "线程名称应该包含自定义前缀"
        threadName != null
        threadName.contains("customer-admin-web")

        cleanup:
        realExecutor.shutdown()
    }

    def "测试线程池资源清理"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        MDC.put("testKey", "testValue")

        def taskMDCBefore = null
        def taskMDCAfter = null
        def latch = new CountDownLatch(1)

        def task = new Runnable() {
            @Override
            void run() {
                taskMDCBefore = MDC.get("testKey")
                // 模拟任务执行后的清理
                MDC.clear()
                taskMDCAfter = MDC.get("testKey")
                latch.countDown()
            }
        }

        when: "提交任务"
        ThreadPoolUtil.submit(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "MDC应该被正确传递和清理"
        taskMDCBefore == "testValue"
        taskMDCAfter == null

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    def "测试线程工厂基本功能"() {
        given: "创建自定义线程工厂"
        def factory = new ThreadPoolUtil.BenefitsThreadFactory("test-pool")
        def task = new Runnable() {
            @Override
            void run() {
                // 空任务
            }
        }

        when: "创建线程"
        def thread = factory.newThread(task)

        then: "线程应该具有正确的属性"
        thread != null
        thread.getName().startsWith("test-pool-")
        thread.getPriority() == Thread.NORM_PRIORITY
        !thread.isDaemon()
    }

    def "测试线程工厂自定义参数"() {
        given: "创建带自定义参数的线程工厂"
        def factory = new ThreadPoolUtil.BenefitsThreadFactory("custom-pool", Thread.MAX_PRIORITY, true)
        def task = new Runnable() {
            @Override
            void run() {
                // 空任务
            }
        }

        when: "创建线程"
        def thread = factory.newThread(task)

        then: "线程应该具有自定义属性"
        thread != null
        thread.getName().startsWith("custom-pool-")
        thread.getPriority() == Thread.MAX_PRIORITY
        thread.isDaemon()
    }

    def "测试线程工厂线程编号递增"() {
        given: "创建线程工厂"
        def factory = new ThreadPoolUtil.BenefitsThreadFactory("numbered-pool")
        def task = new Runnable() {
            @Override
            void run() {
                // 空任务
            }
        }

        when: "创建多个线程"
        def thread1 = factory.newThread(task)
        def thread2 = factory.newThread(task)

        then: "线程编号应该递增"
        thread1.getName().contains("-1")
        thread2.getName().contains("-2")
    }

    def "测试execute方法"() {
        given: "准备一个Runnable任务"
        def task = new Runnable() {
            @Override
            void run() {
                // Mock任务
            }
        }

        when: "执行任务"
        ThreadPoolUtil.execute(task)

        then: "应该调用ExecutorService的execute方法"
        1 * mockExecutorService.execute(_ as Runnable)
    }

    def "测试executeRaw方法"() {
        given: "准备一个原始Runnable任务"
        def task = new Runnable() {
            @Override
            void run() {
                // Mock任务
            }
        }

        when: "执行原始任务"
        ThreadPoolUtil.executeRaw(task)

        then: "应该直接调用ExecutorService的execute方法"
        1 * mockExecutorService.execute(task)
    }

    def "测试executeWithTraceId方法"() {
        given: "准备一个Runnable任务和TraceId"
        def task = new Runnable() {
            @Override
            void run() {
                // Mock任务
            }
        }
        def traceId = "test-trace-id"

        when: "使用指定TraceId执行任务"
        ThreadPoolUtil.executeWithTraceId(task, traceId)

        then: "应该调用ExecutorService的execute方法"
        1 * mockExecutorService.execute(_ as Runnable)
    }

    def "测试submitRaw方法 - Runnable"() {
        given: "准备一个原始Runnable任务"
        def task = new Runnable() {
            @Override
            void run() {
                // Mock任务
            }
        }

        when: "提交原始任务"
        def future = ThreadPoolUtil.submitRaw(task)

        then: "应该直接调用ExecutorService的submit方法"
        1 * mockExecutorService.submit(task) >> mockFuture
        future == mockFuture
    }

    def "测试submitRaw方法 - Callable"() {
        given: "准备一个原始Callable任务"
        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                return "test result"
            }
        }

        when: "提交原始任务"
        def future = ThreadPoolUtil.submitRaw(task)

        then: "应该直接调用ExecutorService的submit方法"
        1 * mockExecutorService.submit(task) >> mockFuture
        future == mockFuture
    }

    def "测试submitWithTraceId方法 - Runnable"() {
        given: "准备一个Runnable任务和TraceId"
        def task = new Runnable() {
            @Override
            void run() {
                // Mock任务
            }
        }
        def traceId = "test-trace-id"

        when: "使用指定TraceId提交任务"
        def future = ThreadPoolUtil.submitWithTraceId(task, traceId)

        then: "应该调用ExecutorService的submit方法"
        1 * mockExecutorService.submit(_ as Runnable) >> mockFuture
        future == mockFuture
    }

    def "测试submitWithTraceId方法 - Callable"() {
        given: "准备一个Callable任务和TraceId"
        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                return "test result"
            }
        }
        def traceId = "test-trace-id"

        when: "使用指定TraceId提交任务"
        def future = ThreadPoolUtil.submitWithTraceId(task, traceId)

        then: "应该调用ExecutorService的submit方法"
        1 * mockExecutorService.submit(_ as Callable) >> mockFuture
        future == mockFuture
    }

    def "测试destroy方法"() {
        when: "调用destroy方法"
        threadPoolUtil.destroy()

        then: "应该正常执行不抛出异常"
        noExceptionThrown()
    }

    def "测试ThreadPoolStatus计算方法"() {
        given: "创建ThreadPoolStatus对象"
        def status = ThreadPoolUtil.ThreadPoolStatus.builder()
            .poolName("TEST")
            .corePoolSize(2)
            .maximumPoolSize(10)
            .activeCount(5)
            .queueSize(3)
            .build()

        when: "计算使用率"
        def poolUsageRate = status.getPoolUsageRate()
        def queueUsageRate = status.getQueueUsageRate()

        then: "应该返回正确的使用率"
        Math.abs(poolUsageRate - 0.5) < 0.001  // 5/10
        Math.abs(queueUsageRate - (3.0 / 13.0)) < 0.001  // 3/(3+10)
    }

    def "测试ThreadPoolStatus计算方法 - 边界情况"() {
        given: "创建边界情况的ThreadPoolStatus对象"
        def statusZeroMax = ThreadPoolUtil.ThreadPoolStatus.builder()
            .maximumPoolSize(0)
            .activeCount(5)
            .queueSize(0)
            .build()

        def statusZeroQueue = ThreadPoolUtil.ThreadPoolStatus.builder()
            .maximumPoolSize(10)
            .activeCount(3)
            .queueSize(0)
            .build()

        when: "计算使用率"
        def poolUsageRateZeroMax = statusZeroMax.getPoolUsageRate()
        def queueUsageRateZeroMax = statusZeroMax.getQueueUsageRate()
        def poolUsageRateZeroQueue = statusZeroQueue.getPoolUsageRate()
        def queueUsageRateZeroQueue = statusZeroQueue.getQueueUsageRate()

        then: "应该正确处理边界情况"
        poolUsageRateZeroMax == 0.0  // maximumPoolSize为0时返回0
        queueUsageRateZeroMax == 0.0  // queueSize为0时返回0
        poolUsageRateZeroQueue == 0.3  // 3/10
        queueUsageRateZeroQueue == 0.0  // queueSize为0时返回0
    }

    def "测试TTL包装器提取ThreadPoolExecutor"() {
        given: "创建一个模拟的TTL包装器"
        def mockTtlWrapper = Mock(ExecutorService) {
            getClass() >> {
                // 创建一个匿名类来模拟TTL包装器
                new Object() {
                    String getName() { return "com.alibaba.ttl.threadpool.ExecutorServiceTtlWrapper" }
                }.getClass()
            }
        }
        def ttlUtil = new ThreadPoolUtil(mockTtlWrapper)
        ttlUtil.init()

        when: "获取线程池状态"
        def status = ThreadPoolUtil.getThreadPoolStatus()

        then: "应该返回null（因为无法提取到真实的ThreadPoolExecutor）"
        status == null
    }

    def "测试extractThreadPoolExecutor异常处理"() {
        given: "创建一个会抛出异常的ExecutorService"
        def problematicExecutor = Mock(ExecutorService) {
            getClass() >> {
                // 创建一个会在反射时抛出异常的类
                new Object() {
                    String getName() { return "com.alibaba.ttl.threadpool.TtlExecutorService" }
                }.getClass()
            }
        }
        def problematicUtil = new ThreadPoolUtil(problematicExecutor)
        problematicUtil.init()

        when: "获取线程池状态"
        def status = ThreadPoolUtil.getThreadPoolStatus()

        then: "应该安全处理异常并返回null"
        status == null
    }

    def "测试execute方法 - 集成测试"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def executed = false
        def latch = new CountDownLatch(1)

        def task = new Runnable() {
            @Override
            void run() {
                executed = true
                latch.countDown()
            }
        }

        when: "执行任务"
        ThreadPoolUtil.execute(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "任务应该被执行"
        executed == true

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    def "测试executeWithTraceId方法 - 集成测试"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def originalTraceId = "execute-trace-id"
        MDC.put("traceId", "current-trace-id")

        def capturedTraceId = null
        def latch = new CountDownLatch(1)

        def task = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
                latch.countDown()
            }
        }

        when: "使用指定TraceId执行任务"
        ThreadPoolUtil.executeWithTraceId(task, originalTraceId)
        latch.await(5, TimeUnit.SECONDS)

        then: "任务中应该获取到指定的TraceId"
        capturedTraceId == originalTraceId

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    def "测试submitRaw方法 - 集成测试"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        // 设置当前线程的TraceId，但submitRaw不应该传递
        MDC.put("traceId", "should-not-be-passed")

        def capturedTraceId = null
        def latch = new CountDownLatch(1)

        def task = new Runnable() {
            @Override
            void run() {
                capturedTraceId = MDC.get("traceId")
                latch.countDown()
            }
        }

        when: "提交原始任务"
        def future = ThreadPoolUtil.submitRaw(task)
        latch.await(5, TimeUnit.SECONDS)

        then: "任务中不应该获取到TraceId"
        capturedTraceId == null
        future.isDone()

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }

    def "测试submitWithTraceId方法 - 集成测试"() {
        given: "使用真实的线程池进行集成测试"
        def realExecutor = Executors.newFixedThreadPool(1)
        def realUtil = new ThreadPoolUtil(realExecutor)
        realUtil.init()

        def specificTraceId = "specific-submit-trace-id"

        def capturedTraceId = null
        def latch = new CountDownLatch(1)

        def task = new Callable<String>() {
            @Override
            String call() throws Exception {
                capturedTraceId = MDC.get("traceId")
                latch.countDown()
                return "result"
            }
        }

        when: "使用指定TraceId提交任务"
        def future = ThreadPoolUtil.submitWithTraceId(task, specificTraceId)
        latch.await(5, TimeUnit.SECONDS)
        def result = future.get()

        then: "任务中应该获取到指定的TraceId"
        capturedTraceId == specificTraceId
        result == "result"

        cleanup:
        realExecutor.shutdown()
        realExecutor.awaitTermination(5, TimeUnit.SECONDS)
    }
}
